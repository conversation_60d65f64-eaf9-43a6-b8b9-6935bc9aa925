import { createSlice } from "@reduxjs/toolkit";
import util from "../../utils";
import { Client, Session, User, ErrorType } from "../../types";
import _session from "../../utils/session";
import store from "../../utils/store";

const initialState: Session = {
  token: _session.getToken(),
  expiry: _session.getExpiry(),
  user: { id: 0, email: "", firstName: "" },
  client: {
    id: 0,
    lightLogo: "",
    darkLogo: "",
    name: "",
    thumbnail: "",
    loader: "",
    loginText: "",
    contactText: "",
    contacts: [],
    homepage: "",
    homepagePath: "",
    features: [],
  },
  theme: {
    primary: "#518cff",
    secondary: "#1b2945",
    success: "#36b37e",
    danger: "#f34115",
    warning: "#fdbc3d",
    info: "#C3D0E8",
  },
  userAgent: util.userAgent,
  ...store.getXHRState(),
};

const sessionSlice = createSlice({
  name: "session",
  initialState,
  reducers: {
    setToken: (state: Session, { payload }: { payload: string }) => {
      state.token = payload;
    },
    setExpiry: (state: Session, { payload }: { payload: string }) => {
      state.expiry = payload;
    },
    setUser: (state: Session, action: { payload: User }) => {
      state.isLoading = false;
      state.user = action.payload;
    },
    setClient: (state: Session, action: { payload: Client }) => {
      state.client = action.payload;
    },
    setLoading: (state: Session, action: { payload: boolean }) => {
      state.isLoading = action.payload;
    },
    setError: (state: Session, action: { payload: ErrorType }) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    reset: () => {
      return initialState;
    },
  },
});

export const sessionReducer = sessionSlice.reducer;

export default sessionSlice.actions;
