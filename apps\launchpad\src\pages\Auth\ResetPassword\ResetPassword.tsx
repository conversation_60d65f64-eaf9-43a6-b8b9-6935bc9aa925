// Node modules
// import React from "react";
// Helpers
import FORM_SCHEMA from "../Password/password-form";
import AuthContainer from "../AuthContainer";
// import { authTokenSelector } from "../auth-selectors";
// import { useCallback } from "@unmatched/hooks";
// import useToastr from "@unmatched/modules/toastr/hook";
// Components
// import { Button, Card, div, Form, Text, Span } from "@repo/ui/components";
import Loader from "../Loader";
import PasswordForm from "../Password/PasswordForm";
import { useNavigate, useParams } from "react-router";
import {  useState } from "@unmatched/hooks";
import { resetFact } from "../auth.api";
import appUrls from "@unmatched/utils/urls/app-urls";
import { Form } from "@repo/ui/components/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Text } from "@repo/ui/components/Text";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { toast } from "sonner";
// interface ParamTypes {
//   email: string;
//   token: string;
// }

const META_DATA = {
  title: "Password Reset",
  content: "Setup a new password for your account",
  loadingTitle: "Resetting password",
  loadingContent: "Please wait while we reset your password",
};

export default function Activation() {
  const navigate = useNavigate();
  const { email, token } = useParams();

  const meta = META_DATA;

  // const { showToast } = useToastr();

  const [state, setState] = useState({
    token,
    email,
    password: "",
    confirmPassword: "",
    tooltip: {
      number: false,
      alphabet: false,
      min: false,
    },
    isLoading: false,
    error: {
      statusCode: 0,
      msg: "",
    },
  });
  // const state = useSelector((store: any) => store.auth.login);

  const onFetchFail = (err: any, payload: any) => {
    // showToast({
    //   show: true,
    //   title: "Error",
    //   variant: "danger",
    //   content: err.msg,
    // });
    toast.error(err.msg);
    if (err.msg === "WRONG_TOKEN" || err.msg === "USER_NOT_FOUND") {
      navigate(appUrls.auth.login);
    }
    setState((state: any) => ({
      ...state,
      isLoading: false,
      ...payload,
      error: err,
    }));
  };

  const onReset = async (data: any) => {
    setState((state: any) => ({
      ...state,
      isLoading: true,
    }));
    try {
      await resetFact(data);
      // showToast({
      //   show: true,
      //   title: "Password Successfully Reset",
      //   variant: "success",
      //   content: "Enter login details to continue",
      // });
      toast.success("Password Successfully Reset", {
        description: "Enter login details to continue",
      });
      navigate(appUrls.auth.login);
    } catch (err) {
      onFetchFail(err, {});
    }
  };

  // const formikOptions = {
  //   initialValues: {
  //     email: email,
  //     token,
  //     password: "",
  //     confirmPassword: "",
  //   },
  //   validationSchema: FORM_SCHEMA,
  //   onSubmit: (values: any) => {
  //     const { email, token } = state;
  //     const { password } = values;
  //     if (email && token) onReset({ email, password, token });
  //   },
  // };

  // const formik = useFormik(formikOptions);
  const form = useForm({
    defaultValues: {
      // email: email,
      // token,
      password: "",
      confirmPassword: "",
    },
    resolver: zodResolver(FORM_SCHEMA),
  });

  const onSubmit = (data: any) => {
    const { email, token } = state;
    const { password } = data;
    if (email && token) onReset({ email, password, token });
  };

  // const onPasswordChange = useCallback(
  //   (e: React.ChangeEvent<any>) => {
  //     formik.handleChange(e);
  //     const tooltip: any = validatePassword(e.target.value);
  //     setState((_state) => ({
  //       ..._state,
  //       tooltip,
  //     }));
  //   },
  //   [formik]
  // );

  let Content = null;

  if (state.isLoading) {
    Content = (
      <Loader title={meta.loadingTitle} content={meta.loadingContent}></Loader>
    );
  } else {
    Content = (
      // onReset={formik.handleReset} onSubmit={formik.handleSubmit}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Text.H2 className="text-primary pb-2">{meta.title}</Text.H2>
          <Text.P1 className="pb-4">
            {meta.content}
            <div className="py-4">
              Account email id:{" "}
              <span className="font-weight-bold">{email}</span>
            </div>
            <div >
              <PasswordForm
                form={form}
                // onPasswordChange={onPasswordChange}
                // tooltip={state.tooltip}
                // colXL={12}
              ></PasswordForm>
            </div>
          </Text.P1>
          <div>
            <Button size="lg" type="submit">
              Reset
            </Button>
          </div>
        </form>
      </Form>
    );
  }
  return (
    <AuthContainer className="col-xl-4 offset-xl-4 col-lg-6 offset-lg-3 w-full">
      {Content}
    </AuthContainer>
  );
}
