import axios, { A<PERSON><PERSON><PERSON><PERSON><PERSON>, InternalAxiosRequestConfig, AxiosResponse } from "axios";
// import logger from "./logger";
// import session from "./session";
// import { util } from "@unmatchedoffl/ui-core";
import log from "./logger";
import session from "./session";

// const { log, session } = util;

const requestInterceptor = (request: InternalAxiosRequestConfig) => {
  const token = session.getToken();
  if (token) {
    return {
      ...request,
      headers: {
        ...request.headers,
        Authorization: `Token ${token}`,
      },
    };
  }
  return request;
};

const responseInterceptor = (response: AxiosResponse) => {
  log.success(response);
  return response;
};

const errorResponseInterceptor = (error: AxiosError) => {
  const errorConfig = {
    msg: "There is some server issue.",
    statusCode: 502,
    reason: {},
    // requestUrl: "",
  };
  if (error.response && error.response.data) {
    // errorConfig.requestUrl = error.response.config.url || "";
    const data = error.response.data as any;
    errorConfig.msg =
      data.detail ||
      data.message ||
      data.reason ||
      data.error;
    errorConfig.statusCode = error.response.status || errorConfig.statusCode;
    if (data.reason) {
      errorConfig.reason = data.reason;
    }
  } else if (error.message) {
    errorConfig.msg = error.message;
  }
  log.error(error);
  return errorConfig;
};

const init = (baseURL: string) => {
  axios.defaults.baseURL = baseURL || ""; //eslint-disable-line
};

const addInterceptors = () => {
  axios.interceptors.response.use(responseInterceptor, (err: any) => {
    const { statusCode, msg, reason } = errorResponseInterceptor(err);
    const errMsg = msg || "An error occurred";
    if ([401, "401"].includes(statusCode)) {
      log.error("Unauthorized", errMsg);
      session.onUnAuthorize();
    }
    return Promise.reject({ statusCode, msg: errMsg, reason, data: err.response.data });
  });

  axios.interceptors.request.use(requestInterceptor);
};

const getConfigurations = (_params: any, _meta: any) => {
  const params = _params || {};
  const meta = _meta || {};
  return {
    params,
    ...meta,
  };
};

const api = {
  init,
  addInterceptors,
  getConfigurations,
};

export default api;
