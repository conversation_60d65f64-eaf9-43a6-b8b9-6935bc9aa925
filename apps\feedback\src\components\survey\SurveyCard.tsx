import { ClockIcon } from './Icons';
import { getSurveyTypeConfig } from '../../utils/surveyUtils';

interface SurveyCardProps {
  survey: any;
  statsMap: any;
  isOngoing: boolean;
  onViewDetails: (surveyId: string) => void;
}

export const SurveyCard = ({ survey, statsMap, isOngoing, onViewDetails }: SurveyCardProps) => {
  const getDaysLeft = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const now = new Date();
    return Math.ceil((deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const progress = statsMap[survey.id]?.percentage_completed || 0;
  const daysLeft = survey.deadline ? getDaysLeft(survey.deadline) : null;
  const surveyTypeConfig = getSurveyTypeConfig(survey.resourcetype);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow overflow-hidden">
      {/* Title section with background color */}
      <div className={`${surveyTypeConfig.bgColor} ${surveyTypeConfig.borderColor} border-b dark:border-gray-600 px-4 py-3`}>
        <div className="flex items-start justify-between">
          <div className="flex flex-col gap-1">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {survey.title || 'Survey Title'}
            </h3>
            <span className={`text-xs font-medium ${surveyTypeConfig.textColor}`}>
              {surveyTypeConfig.displayName}
            </span>
          </div>
          {isOngoing && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-700">
              Active
            </span>
          )}
        </div>
      </div>

      {/* Content section */}
      <div className="p-4">
        <div className="flex flex-col gap-2 mb-2">
          {daysLeft !== null && daysLeft < 8 && (
            <div className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 w-fit">
              <ClockIcon />
              <span>ENDING SOON</span>
            </div>
          )}
        </div>

        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          {survey.description || 'No description available'}
        </p>
        <div className="flex items-center text-sm mb-3">
          <span className='mr-2'>Due date:</span>
          <span className="text-gray-800 dark:text-gray-400">
            <b>{survey.deadline ? `${formatDate(survey.deadline)}` : 'No deadline'}</b>
          </span>
          {/* <button
            onClick={() => onViewDetails(survey.id)}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
          >
            View Details
          </button> */}
        </div>
        <div className="mb-4">

          {/* Stats Counts */}
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="flex flex-col items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <span className="font-medium text-gray-900 dark:text-white">
                {statsMap[survey.id]?.questions_completed || 0}
              </span>
              <span className="text-gray-500 dark:text-gray-400 text-xs">Completed</span>
            </div>
            <div className="flex flex-col items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <span className="font-medium text-gray-900 dark:text-white">
                {statsMap[survey.id]?.questions_left || 0}
              </span>
              <span className="text-gray-500 dark:text-gray-400 text-xs">Remaining</span>
            </div>
            <div className="flex flex-col items-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
              <span className="font-medium text-blue-700 dark:text-blue-300">
                {statsMap[survey.id]?.total_questions || 0}
              </span>
              <span className="text-blue-600 dark:text-blue-400 text-xs">Total</span>
            </div>
          </div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
          <span>{Math.round(progress) ? 'Progress' : 'Yet to start'}</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-3">
          <div
            className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full"
            style={{ width: `${progress}%` }}
          />
        </div>


      </div>
    </div>
  );
};

export const SurveyCardSkeleton = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 animate-pulse overflow-hidden">
    {/* Title section skeleton */}
    <div className="bg-gray-50 dark:bg-gray-700 border-b dark:border-gray-600 px-4 py-3">
      <div className="h-5 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
      <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/3"></div>
    </div>

    {/* Content section skeleton */}
    <div className="p-4">
      <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/2 mb-4"></div>
      <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-full mb-2"></div>
      <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-5/6 mb-4"></div>
      <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
    </div>
  </div>
);
