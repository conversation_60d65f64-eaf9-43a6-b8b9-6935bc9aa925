import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, X, FileText, AlertCircle, CheckCircle2 } from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Alert, AlertDescription } from "@repo/ui/components/alert";
import { Progress } from "@repo/ui/components/progress";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { employeeFileUpload, updateEmployeeFileUploadTags } from "../employeesService";

// Import the response type from the service
import { EmployeeFileUploadResponse } from "../employeesService";

// Use the imported type as our UploadResponse
type UploadResponse = EmployeeFileUploadResponse;

interface FileUploadModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export const FileUploadModal: React.FC<FileUploadModalProps> = ({
  open,
  onOpenChange,
  onSuccess,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [fileId, setFileId] = useState<string | null>(null);
  const [title, setTitle] = useState("");
  const [tags, setTags] = useState("");
  const [uploadResponse, setUploadResponse] = useState<UploadResponse | null>(null);
  const [uploadStep, setUploadStep] = useState<"upload" | "details" | "uploading" | "complete">("upload");

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setFile(acceptedFiles[0]);
      setError(null);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    maxFiles: 1,
  });

  // Move to details step to collect title and tags
  const handleProceedToDetails = () => {
    if (!file) {
      setError("Please select a file to upload");
      return;
    }

    // Set default title from filename
    if (!title) {
      setTitle(file.name);
    }

    // Move to details step
    setUploadStep("details");
  };

  // Start the actual upload process after collecting title and tags
  const handleStartUpload = async () => {
    if (!file) {
      setError("Please select a file to upload");
      return;
    }

    if (!title.trim()) {
      setError("Title is required");
      return;
    }

    setUploading(true);
    setError(null);
    setUploadProgress(0);
    setUploadStep("uploading");

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const newProgress = prev + 10;
          if (newProgress >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return newProgress;
        });
      }, 300);

      // Upload the file
      const response = await employeeFileUpload({
        data: file,
        format: file.name.split('.').pop() || 'xlsx',
        title: title,
      });

      // Complete the progress bar
      clearInterval(progressInterval);
      setUploadProgress(100);
      setSuccess(true);

      console.log("Upload response:", response);

      // The response should already have a standardized structure from the service
      const fileId = response.id;

      console.log("Using file ID for tag update:", fileId);

      // Set file ID and response data in state
      if (fileId) {
        setFileId(fileId);
      }

      // The response is already standardized by the service
      setUploadResponse(response);

      // Update tags using the real file ID
      try {
        // Convert comma-separated tags to array
        const tagsArray = tags.split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);

        console.log("Updating tags for file ID:", fileId, "Tags:", tagsArray);

        // Update the file with tags - even if tags array is empty, we still need to make the call
        // to ensure the title is properly associated with the file
        if (fileId) {
          await updateEmployeeFileUploadTags(fileId, tagsArray);
        }
      } catch (updateError) {
        console.error("Error updating tags, but continuing:", updateError);
        // Continue even if tag update fails - this is non-critical
      }

      // Move to complete step
      setUploadStep("complete");

      // Notify parent component of success
      onSuccess();
    } catch (err) {
      console.error("Error uploading file:", err);
      setError(err instanceof Error ? err.message : "Failed to upload file. Please try again.");
      setUploading(false);
      // Go back to details step on error
      setUploadStep("details");
    }
  };



  const handleCloseModal = () => {
    onOpenChange(false);
    // Reset state after modal is closed
    setTimeout(() => {
      setFile(null);
      setFileId(null);
      setTitle("");
      setTags("");
      setUploading(false);
      setUploadProgress(0);
      setSuccess(false);
      setUploadResponse(null);
      setUploadStep("upload");
      setError(null);
    }, 300);
  };

  const handleCancel = () => {
    if (uploading) return; // Prevent canceling during upload
    handleCloseModal();
  };

  const removeFile = () => {
    setFile(null);
    setError(null);
  };

  return (
    <Dialog open={open} onOpenChange={uploading ? () => {} : onOpenChange}>
      <DialogContent className="sm:max-w-[798px]">
        <DialogHeader>
          <DialogTitle>
            {uploadStep === "upload" && "Upload Employee File"}
            {uploadStep === "details" && "File Details"}
            {uploadStep === "complete" && "Upload Complete"}
          </DialogTitle>
          <DialogDescription>
            {uploadStep === "upload" && "Upload an Excel or CSV file containing employee information."}
            {uploadStep === "details" && "Add a title and optional tags for your uploaded file."}
            {uploadStep === "complete" && "Your file has been processed successfully."}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {/* Step 1: File Upload */}
          {uploadStep === "upload" && (
            <>
              {/* Dropzone area */}
              {!file && (
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive
                      ? "border-primary bg-primary/5"
                      : "border-gray-300 hover:border-primary/50"
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="h-10 w-10 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm font-medium mb-1">
                    Drag & drop your file here, or click to select
                  </p>
                  <p className="text-xs text-gray-500">
                    Supported formats: .xlsx, .xls, .csv
                  </p>
                </div>
              )}

              {/* Selected file */}
              {file && (
                <div className="border rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-8 w-8 text-primary" />
                      <div>
                        <p className="font-medium">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                    {!uploading && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={removeFile}
                        className="h-8 w-8"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Step 2: File Details - Before Upload */}
          {uploadStep === "details" && (
            <>
              {/* File Information */}
              <div className="border rounded-lg p-4 mb-6">
                <h3 className="font-medium mb-2">1 File selected</h3>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-primary" />
                    <span className="text-sm">{file?.name}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 px-2 py-1 h-auto text-xs"
                    onClick={() => {
                      removeFile();
                      setUploadStep("upload");
                    }}
                  >
                    Remove
                  </Button>
                </div>

                {/* Title Input */}
                <div className="space-y-2 mb-4">
                  <Label htmlFor="title" className="text-sm">
                    Title <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter a title for this file"
                    required
                  />
                </div>

                {/* Tags Input */}
                <div className="space-y-2">
                  <Label htmlFor="tags" className="text-sm">
                    Tags
                  </Label>
                  <Input
                    id="tags"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    placeholder="Enter comma-separated tags (e.g. finance, 2023)"
                  />
                  <p className="text-xs text-gray-500">
                    Separate tags with commas
                  </p>
                </div>
              </div>
            </>
          )}

          {/* Step 3: Uploading with Progress */}
          {uploadStep === "uploading" && (
            <>
              <div className="text-center py-6">
                <div className="mx-auto mb-6">
                  <Upload className="h-12 w-12 mx-auto mb-4 text-primary animate-pulse" />
                </div>
                <h3 className="text-xl font-medium mb-4">Uploading File</h3>

                <div className="max-w-md mx-auto mb-6">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Uploading...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                </div>

                <p className="text-gray-600 mb-2">
                  Uploading {file?.name}
                </p>
                <p className="text-sm text-gray-500">
                  Please wait while your file is being processed
                </p>
              </div>
            </>
          )}

          {/* Step 4: Complete with Results */}
          {uploadStep === "complete" && uploadResponse && (
            <>
              {/* Upload Results */}
              <div className="space-y-4 mb-6">
                {/* Mandatory fields verification */}
                <div className="flex items-start gap-3">
                  <div className="mt-1 bg-green-100 rounded-full p-1">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-green-600">Mandatory fields are verified</h3>
                    <p className="text-sm text-gray-600">All the fields are intact to upload employee records</p>
                  </div>
                </div>

                {/* Records added/updated */}
                <div className="flex items-start gap-3">
                  <div className="mt-1 bg-green-100 rounded-full p-1">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-green-600">All records are added/updated</h3>
                    <p className="text-sm text-gray-600">
                      {uploadResponse.users_added_count !== undefined
                        ? `${uploadResponse.users_added_count} new records found and added.`
                        : "0 new records found and added."}
                    </p>
                    <p className="text-sm text-gray-600">
                      {uploadResponse.users_updated_count !== undefined
                        ? `${uploadResponse.users_updated_count} existing records have been updated.`
                        : "0 existing records have been updated."}
                    </p>
                    <p className="text-sm text-gray-600">
                      {uploadResponse.total_file_records !== undefined
                        ? `${uploadResponse.total_file_records} records found and validated in total.`
                        : ""}
                    </p>
                  </div>
                </div>
              </div>

              {/* File Information */}
              <div className="border rounded-lg p-4 mb-6">
                <h3 className="font-medium mb-2">1 File uploaded</h3>
                <div className="flex items-center gap-3 mb-2">
                  <div className="bg-green-100 rounded-full p-1">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                  </div>
                  <span className="text-sm font-medium">{title}</span>
                </div>

                {tags && (
                  <div className="flex items-start gap-2 ml-8">
                    <span className="text-xs text-gray-500">Tags:</span>
                    <div className="flex flex-wrap gap-1">
                      {tags.split(',').map((tag, index) => (
                        tag.trim() && (
                          <span
                            key={index}
                            className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full"
                          >
                            {tag.trim()}
                          </span>
                        )
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </>
          )}



          {/* Error message */}
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between gap-2">
          {uploadStep === "upload" && (
            <>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={uploading}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleProceedToDetails}
                disabled={!file || uploading}
                className="flex items-center gap-2"
              >
                Next
              </Button>
            </>
          )}

          {uploadStep === "details" && (
            <>
              <Button
                type="button"
                variant="outline"
                onClick={() => setUploadStep("upload")}
                disabled={uploading}
              >
                Back
              </Button>
              <Button
                type="button"
                onClick={handleStartUpload}
                disabled={!title || uploading}
              >
                Upload File
              </Button>
            </>
          )}

          {uploadStep === "uploading" && (
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={true}
            >
              Cancel
            </Button>
          )}

          {uploadStep === "complete" && (
            <Button
              type="button"
              onClick={handleCloseModal}
              className="ml-auto"
            >
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
