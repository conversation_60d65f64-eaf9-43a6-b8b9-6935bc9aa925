import { useState, useEffect } from "react";
import { <PERSON> } from "react-router";
// import {
//   Button,
//   div,
//   Form,
//   FormControl,
//   FormGroup,
//   Icon,
//   Text,
// } from "unmatched/components";
import appUrls from "../../../unmatched/utils/urls/app-urls";
// import { useFormik } from "unmatched/hooks";
// import Loader from "../Loader";
// import $icons from "assets/icons/icons";
import { ViewStates } from "./Login";
// import styled from "styled-components";

// import util from "unmatched/utils";
import { OktaLogin } from "../OktaLogin";
import useSession from "../../../unmatched/modules/session/hook";
import {
  Form,
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/form";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@repo/ui/components/button";
import { Text } from "@repo/ui/components/Text";
import { Input } from "@repo/ui/components/input";
import { Eye, EyeClosed, LinkIcon, Loader2, MailCheck } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import Loader from "../Loader";

// const { yup, getFieldErrorMessage, isFieldInvalid, isFieldValid } = util.formik;

const META_DATA = {
  // title: "Resend activation",
  content: "Enter the email address associated with your account",
  loadingTitle: "Check your mailbox",
  loadingContent:
    "Activation link has been emailed to you. Click the link in the email to activate your account",
};

// const StyledIcon = styled(Icon)`
//   position: absolute;
//   right: 60px;
//   margin-top: -23px;
// `;

const LoginForm = (props: any) => {
  const {
    hasPassword,
    onSubmit,
    Feedback,
    submit,
    loading,
    meta,
    sentActivation,
    requestMagicLink,
    sentMagicLink,
    isAdmin,
    loginState,
    showSubmit = true,
    isPasswordSet = true,
    hasMagicLink = false,
  } = props;

  const [metas, setMetas] = useState(META_DATA);
  const [showPassword, setShowPassword] = useState(false);
  const session = useSession();

  useEffect(() => {
    setMetas(meta);
  }, [meta]);

  const schema = z.object({
    email: z.string().email(),
    ...(hasPassword && { password: z.string().min(8) }),
  });

  const form = useForm({
    resolver: zodResolver(schema),
    // mode: "all",
    defaultValues: {
      email: "",
      ...(hasPassword && { password: "" }),
    },
  });

  // let initialValues: any = { email: payload.email };
  // let validationSchema: any = {
  //   email: yup
  //     .string()
  //     .email("Enter a valid email id")
  //     .required("Enter your email id"),
  // };
  // if (props.hasPassword) {
  //   initialValues = {
  //     ...initialValues,
  //     password: payload.password,
  //   };
  //   validationSchema = {
  //     ...validationSchema,
  //     password: yup.string().required("Enter your password"),
  //   };
  // }
  // const formik = useFormik({
  //   initialValues,
  //   validateOnChange: true,
  //   validateOnBlur: false,
  //   validationSchema: yup.object().shape(validationSchema),
  //   onSubmit: (values) => {
  //     onSubmit && onSubmit(values);
  //   },
  // });

  // const onPasswordChange = (e: any) => {
  //   if (incorrectPassword) {
  //     clearAjaxError();
  //   }
  //   formik.handleChange(e);
  // };

  // const inValidPassword =
  //   isFieldInvalid(formik, "password") || incorrectPassword;

  // const onEmailBlur = (evt: any) => {
  //   formik.handleBlur(evt);
  //   if (updateLoginState) updateLoginState(evt);
  // };

  // const onEmailChange = (evt: any) => {
  //   formik.handleChange(evt);
  //   if (updateLoginState) updateLoginState(evt);
  // };

  if (sentActivation || sentMagicLink) {
    return (
      <Loader
        title={metas.loadingTitle}
        iconTemplate={<MailCheck className="size-12 mx-auto text-primary" />}
        content={metas.loadingContent}
      />
    );
  }

  const renderRequestMagicLink = () => {
    if (hasMagicLink && !isAdmin) {
      return (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            requestMagicLink({ email: form.getValues("email") });
          }}
          className="text-xs pt-1 bg-primary w-full rounded text-white flex py-2"
        >
          <div className="pl-3 pr-6 border-r">
            {/* <Icon icon="far fa-link" /> */}
            <LinkIcon />
          </div>
          <div className="flex-grow text-center ">Passwordless login</div>
        </Button>
      );
    }
  };

  // const PasswordTextInput = !showPassword
  //   ? FormControl.Password
  //   : FormControl.Text;

  //onReset={formik.handleReset} onSubmit={formik.handleSubmit}

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Text.H2 className="text-primary pb-2 text-xl !mb-0 font-medium">{meta.title}</Text.H2>
          <Text.P1 className="pb-2">{meta.content}</Text.P1>
          <div className="pt-4 pb-3">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Email address" {...field} disabled={hasPassword} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormGroup>
            <FormGroup.Label>Email</FormGroup.Label>
            <FormControl.Email
              name="email"
              isInvalid={isFieldInvalid(formik, "email")}
              disabled={hasPassword}
              isValid={hasPassword}
              autoFocus
              value={formik.values["email"]}
              onBlur={onEmailBlur}
              onChange={onEmailChange}
              placeholder="Email address"
            />
            <FormGroup.InValidFeedback
              text={getFieldErrorMessage(formik, "email")}
            />
          </FormGroup> */}
            {hasPassword && isPasswordSet && (
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="relative my-4">
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter password"
                        type={showPassword ? "text" : "password"}
                        {...field}
                      />
                    </FormControl>
                    <div onClick={() => setShowPassword((s) => !s)} className="absolute right-3 top-8">
                      {showPassword ? <EyeClosed className="size-4" /> : <Eye className="size-4" />}
                    </div>
                    {/* <FormMessage /> */}
                  </FormItem>
                )}
              />

              // <FormField
              //   control={form.control}
              //   name="password"
              //   render={({ field }) => (
              //     <FormItem>
              //       <FormLabel>Password</FormLabel>
              //       <FormControl>
              //         <Input
              //           placeholder="Enter password"
              //           {...field}
              //           type={showPassword ? "text" : "password"}
              //         />
              //         <div onClick={() => setShowPassword((s) => !s)}>
              //           {showPassword ? <EyeClosed /> : <Eye />}
              //         </div>
              //       </FormControl>
              //       <FormMessage />
              //     </FormItem>
              //   )}
              // />
              // <FormGroup>
              //   <FormGroup.Label>Password</FormGroup.Label>
              //   <PasswordTextInput
              //     name="password"
              //     autoFocus
              //     value={formik.values?.password || ""}
              //     isInvalid={!!inValidPassword}
              //     isValid={isFieldValid(formik, "password")}
              //     onBlur={formik.handleBlur}
              //     onChange={onPasswordChange}
              //     placeholder="Enter password"
              //   />
              //   <div onClick={() => setShowPassword((s) => !s)}>
              //     <StyledIcon
              //       className="fs-14"
              //       icon={`fas ${showPassword ? "fa-eye-slash" : "fa-eye"}`}
              //     />
              //   </div>
              //   <FormGroup.InValidFeedback
              //     text={
              //       getFieldErrorMessage(formik, "password") || inValidPassword
              //     }
              //   />
              // </FormGroup>
            )}
            {Feedback}
          </div>
          <div
            className={
              !hasPassword ? "pt-4 flex" : "flex justify-between items-center"
            }
          >
            {hasPassword && (
              <div className="" style={{ minWidth: "160px" }}>
                <Link to={appUrls.auth.requestPassword} className="text-sm pt-1">
                  Forgot your password?
                </Link>
              </div>
            )}
            {/* loginState === ViewStates.IN_ACTIVE */}
            {showSubmit && (
              <div
                className={
                  loginState === ViewStates.ACTIVE
                    ? "inline"
                    : "w-full flex flex-row justify-end"
                }
              >
                <Button disabled={loading} type="submit">
                  {loading && <Loader2 className="animate-spin" />}{" "}
                  {submit.label}
                </Button>
              </div>
            )}
          </div>
          {(loginState === ViewStates.IN_ACTIVE ||
            loginState === ViewStates.ACTIVE) &&
            hasMagicLink &&
            !isAdmin && (
              <div className="flex flex-row items-center my-3">
                <div
                  className="border-t flex-grow"
                  style={{ height: 0 }}
                ></div>
                <div className="px-2 text-xs text-dark">OR</div>
                <div
                  className="border-t flex-grow"
                  style={{ height: 0 }}
                ></div>
              </div>
            )}
          {(loginState === ViewStates.IN_ACTIVE ||
            loginState === ViewStates.ACTIVE) &&
            !isAdmin &&
            renderRequestMagicLink()}
          {/* {!isAdmin && renderRequestMagicLink()}v2 */}
          {/* <div
          className={
            !hasPassword
              ? "pt-4 flex"
              : "flex justify-between items-end"
          }
        ></div> */}
        </form>
      </Form>
      {(session?.client as any)?.sso?.enabled && <OktaLogin />}
    </>
  );
};

export default LoginForm;
