import { useState } from "react";
// import { CustomModal as Modal } from "unmatched/components";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  // DialogFooter,
  // DialogClose,
} from "@repo/ui/components/dialog";
import useSession from "@/unmatched/modules/session/hook";
import ChangePassword from "../Auth/ChangePassword/ChangePassword";
// import { Button } from "@repo/ui/components/button";

export default function ChangePasswordModal(props: any) {
  const {
    user,
    user: { isPasswordSet },
  } = useSession();
  const [passSetComplete, setPassSetComplete] = useState(false);
  return (
    <Dialog {...props}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {!isPasswordSet || passSetComplete
              ? "Set Password"
              : "Change Password"}
          </DialogTitle>
        </DialogHeader>
        <ChangePassword
          setPassSetComplete={() => setPassSetComplete(true)}
          closeModal={props.onOpenChange}
          user={user}
        />
        {/* <DialogFooter >
          <DialogClose asChild>
            <Button type="button" variant="outline">
              Close
            </Button>
          </DialogClose>
        </DialogFooter> */}
      </DialogContent>
    </Dialog>
  );
}
