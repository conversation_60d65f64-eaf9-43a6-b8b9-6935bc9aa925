import React from "react";
import { TableBody, TableCell, TableRow } from "@repo/ui/components/table";
import { Loader2, Download, FileWarning, Trash2, MoreHorizontal } from "lucide-react";
import { File } from "../filesService";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

interface FileTableBodyProps {
  loadingFiles: boolean;
  files: File[];
  onRowClick?: (file: File) => void;
  onDeleteFile?: (id: string) => Promise<boolean>;
  onDownloadFile?: (fileUrl: string, fileId?: string) => void;
  onDownloadErrorLog?: (errorLogUrl: string, fileId?: string) => void;
}

export const FileTableBody: React.FC<FileTableBodyProps> = ({
  loadingFiles,
  files,
  onRowClick,
  onDeleteFile,
  onDownloadFile,
  onDownloadErrorLog,
}) => {
  // Calculate total number of columns
  const totalColumns = 7;

  const handleRowClick = (file: File) => {
    if (onRowClick) {
      onRowClick(file);
    }
  };

  // const formatDate = (dateString: string) => {
    if (!dateString) {
      return "N/A";
    }

    try {
      // Format: "2025-03-21T08:23:36.496672Z"
      // This is ISO 8601 format and should be parsed correctly by the Date constructor
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn("Invalid date:", dateString);
        return dateString; // Return the original string if we can't parse it
      }

      // Format the date
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      return date.toLocaleDateString('en-US', options);
    } catch (error) {
      console.error("Error formatting date:", error, dateString);
      return dateString; // Return the original string on error
    }
  };

  if (loadingFiles) {
    return (
      <TableBody>
        <TableRow>
          <TableCell colSpan={totalColumns} className="h-24 text-center">
            <div className="flex justify-center items-center">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading files...</span>
            </div>
          </TableCell>
        </TableRow>
      </TableBody>
    );
  }

  if (files.length === 0) {
    return (
      <TableBody>
        <TableRow>
          <TableCell colSpan={totalColumns} className="h-24 text-center">
            No files found.
          </TableCell>
        </TableRow>
      </TableBody>
    );
  }

  return (
    <TableBody>
      {files.map((file) => (
        <TableRow
          key={file.id}
          className={onRowClick ? "cursor-pointer hover:bg-muted/50" : ""}
          onClick={onRowClick ? () => handleRowClick(file) : undefined}
        >
          <TableCell className="font-medium">{file.title}</TableCell>
          <TableCell>{file.successRecords}</TableCell>
          <TableCell>{file.rejectedRecords}</TableCell>
          <TableCell>{file.totalRecords}</TableCell>
          <TableCell>
            <div className="flex flex-wrap gap-1">
              {file.tags && file.tags.length > 0 ? (
                file.tags.map((tag, index) => (
                  <Badge key={index} variant="outline">
                    {tag}
                  </Badge>
                ))
              ) : (
                <span className="text-muted-foreground">No tags</span>
              )}
            </div>
          </TableCell>
          <TableCell>
            {file.uploadedOn && file.uploadedOn !== "N/A"
              ? (() => {
                  try {
                    const date = new Date(file.uploadedOn);
                    return date.toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    });
                  } catch (error) {
                    return file.uploadedOn;
                  }
                })()
              : "N/A"}
          </TableCell>
          <TableCell>
            <div className="flex items-center justify-center" onClick={(e) => e.stopPropagation()}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 p-0"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">Open menu</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => {
                      if (onDownloadFile) {
                        try {
                          // Pass both the file URL and file ID
                          onDownloadFile(file.fileUrl, file.id);
                        } catch (error) {
                          console.error("Error downloading file:", error);
                          alert("File not available for download.");
                        }
                      }
                    }}
                    className="cursor-pointer"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    <span>Download File</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => {
                      if (onDownloadErrorLog) {
                        try {
                          // Pass both the error log URL and file ID
                          onDownloadErrorLog(file.errorLogFile || "", file.id);
                        } catch (error) {
                          console.error("Error downloading rejected records:", error);
                          alert("No rejected records available for this file.");
                        }
                      }
                    }}
                    className="cursor-pointer"
                  >
                    <FileWarning className="h-4 w-4 mr-2" />
                    <span>Download Rejected Records</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={async () => {
                      if (onDeleteFile) {
                        const confirmed = window.confirm("Are you sure you want to delete this file?");
                        if (confirmed) {
                          await onDeleteFile(file.id);
                        }
                      }
                    }}
                    className="cursor-pointer text-red-500 focus:text-red-500"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    <span>Delete</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  );
};
